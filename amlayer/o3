initializing
creating index
opening index
inserting into index
deleting odd number records
retrieving even number records
0
3538970
2
3538970
4
3538970
6
3538970
8
3538970
10
3538970
12
3538970
14
3538970
16
18
retrieved 18 records
deleting even number records
retrieving from empty index
retrieved 0 records
begin test of complex delete
inserting into index
deleting everything
printing empty index
retrieved 0 records
inserting everything back
delete records less than 100
delete records greater than 150
printing between 100 and 150
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
retrieved 51 records
closing down
test3 done!
